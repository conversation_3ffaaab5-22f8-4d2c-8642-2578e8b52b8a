package org.dromara.sol.test;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.SolanaTransactionScanner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Solana全量扫描时间阈值测试类
 * 用于验证10分钟间隔阈值功能
 */
@Slf4j
@Component
public class SolanaFullScanThresholdTest {

    @Autowired
    private SolanaTransactionScanner solanaTransactionScanner;

    /**
     * 测试全量扫描时间阈值功能
     * 连续调用多次全量扫描，验证时间阈值是否生效
     */
    public void testFullScanThreshold() {
        log.info("开始测试全量扫描时间阈值功能...");
        
        // 打印当前扫描状态
        log.info("测试前状态:\n{}", solanaTransactionScanner.getFullScanStatus());
        
        // 第一次扫描 - 应该执行
        log.info("=== 第一次扫描测试 ===");
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
        
        // 等待1秒
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 打印第一次扫描后的状态
        log.info("第一次扫描后状态:\n{}", solanaTransactionScanner.getFullScanStatus());
        
        // 第二次扫描 - 应该被跳过（时间间隔不足）
        log.info("=== 第二次扫描测试（应该被跳过）===");
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
        
        // 第三次扫描 - 应该被跳过（时间间隔不足）
        log.info("=== 第三次扫描测试（应该被跳过）===");
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
        
        // 打印最终状态
        log.info("测试完成后状态:\n{}", solanaTransactionScanner.getFullScanStatus());
        
        log.info("全量扫描时间阈值功能测试完成");
    }
    
    /**
     * 获取全量扫描状态信息
     * 可以通过REST接口或其他方式调用此方法来监控扫描状态
     */
    public String getFullScanStatusInfo() {
        return solanaTransactionScanner.getFullScanStatus();
    }
    
    /**
     * 模拟系统重启场景的测试
     */
    public void testSystemRestartScenario() {
        log.info("=== 模拟系统重启场景测试 ===");
        
        // 模拟系统启动时的全量扫描
        log.info("模拟系统启动时的全量扫描...");
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
        
        // 模拟短时间内的WebSocket重连
        log.info("模拟5秒后的WebSocket重连...");
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 这次应该被跳过
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
        
        log.info("系统重启场景测试完成");
    }
}
