# Solana 扫描配置示例
# 这个配置文件展示了如何配置全量扫描的时间阈值

solana:
  # 补偿机制配置
  compensation:
    # 初始延迟时间（分钟），系统启动后多久开始第一次补偿检查
    initial-delay-minutes: 2
    
  # 全量扫描配置
  full-scan:
    # 最小扫描间隔（分钟），防止频繁全量扫描
    # 默认值：10分钟
    # 建议值：5-30分钟，根据业务需求调整
    min-interval-minutes: 10

# 使用说明：
# 1. 将此配置添加到你的 application.yml 或 application.properties 中
# 2. 根据业务需求调整 min-interval-minutes 的值
# 3. 较小的值（如5分钟）适合对实时性要求高的场景
# 4. 较大的值（如30分钟）适合减少系统负载的场景
# 5. 系统会在日志中输出扫描状态信息，便于监控

# 配置示例（application.yml格式）：
# solana:
#   compensation:
#     initial-delay-minutes: 2
#   full-scan:
#     min-interval-minutes: 10

# 配置示例（application.properties格式）：
# solana.compensation.initial-delay-minutes=2
# solana.full-scan.min-interval-minutes=10
