package org.dromara.sol.websocket.impl;

import cn.hutool.core.thread.ThreadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.manager.SolTransactionManager;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.dromara.sol.manager.SolHttpManager;
import org.dromara.sol.websocket.SolanaTransactionScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Solana交易扫描器实现
 * 负责扫描历史交易，实现补偿机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaTransactionScannerImpl implements SolanaTransactionScanner {

    private final SolHttpManager solHttpManager;
    private final SolTransactionManager solTransactionManager;
    private final SolMonitorManager solMonitorManager;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    // 遗漏交易检查相关参数
    private static final int MAX_DAILY_API_REQUESTS = 60000; // 每天最大API请求次数
    private static final int API_REQUESTS_PER_HOUR = MAX_DAILY_API_REQUESTS / 24; // 每小时请求次数
    private static final int MAX_TRANSACTION_HISTORY = 6; // 每次检查获取的历史交易数量
    private static final int CHECK_INTERVAL_MINUTES = 60; // 完整检查周期（分钟）

    // 新增：可配置的初始延迟时间（分钟），默认为2分钟
    @Value("${solana.compensation.initial-delay-minutes:2}")
    private long initialDelayMinutes;

    // 新增：全量扫描间隔阈值（分钟），默认为10分钟
    @Value("${solana.full-scan.min-interval-minutes:10}")
    private long fullScanMinIntervalMinutes;

    // 用于追踪API请求次数的计数器
    private final AtomicLong dailyApiRequestCount = new AtomicLong(0);
    private volatile LocalDateTime apiCounterResetTime = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0);

    // 并发控制标志，防止补偿任务重复执行
    private final AtomicBoolean compensationRunning = new AtomicBoolean(false);

    // 补偿统计指标
    private final AtomicLong totalCompensationAttempts = new AtomicLong(0);
    private final AtomicLong successfulCompensations = new AtomicLong(0);
    private final AtomicLong failedCompensations = new AtomicLong(0);
    private final AtomicLong totalTransactionsCompensated = new AtomicLong(0);
    private final Map<String, LocalDateTime> lastAddressCompensation = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> addressCompensationCount = new ConcurrentHashMap<>();

    // 遗漏交易检查的调度器
    private final ScheduledExecutorService transactionCheckScheduler = new ScheduledThreadPoolExecutor(1, r -> {
        Thread thread = new Thread(r, "solana-transaction-checker");
        thread.setDaemon(true);
        return thread;
    });

    // 定时任务的Future，用于取消和重新调度
    private volatile ScheduledFuture<?> scheduledTask;

    @Override
    public void startMissedTransactionChecker() {
        log.info("启动遗漏交易检查定时任务");

        // 启动重置计数器的定时任务
        transactionCheckScheduler.scheduleAtFixedRate(() -> {
            try {
                setTenant();
                LocalDateTime now = LocalDateTime.now();
                if (now.isAfter(apiCounterResetTime)) {
                    dailyApiRequestCount.set(0);
                    apiCounterResetTime = now.plusDays(1).withHour(0).withMinute(0).withSecond(0);
                    log.info("API请求计数器已重置，新的计数周期开始");
                }
            } catch (Exception e) {
                log.error("重置API计数器时发生错误", e);
            }
        }, 1, 1, TimeUnit.HOURS);

        // 启动可动态重置的遗漏交易检查任务
        scheduleNextCompensationCheck();
    }

    /**
     * 调度下一次补偿检查任务
     */
    private void scheduleNextCompensationCheck() {
        // 取消现有的定时任务（如果存在）
        if (scheduledTask != null && !scheduledTask.isDone()) {
            scheduledTask.cancel(false);
        }

        // 安排新的定时任务，使用配置的初始延迟
        scheduledTask = transactionCheckScheduler.scheduleAtFixedRate(() -> {
            try {
                if (compensationRunning.compareAndSet(false, true)) {
                    try {
                        setTenant();
                        log.info("定时任务触发：开始执行遗漏交易补偿检查");
                        scheduleAddressChecks();
                    } finally {
                        compensationRunning.set(false);
                    }
                } else {
                    log.info("补偿任务正在执行中，跳过本次定时触发");
                }
            } catch (Exception e) {
                compensationRunning.set(false);
                log.error("定时补偿检查执行异常", e);
            }
        }, initialDelayMinutes, CHECK_INTERVAL_MINUTES, TimeUnit.MINUTES);

        log.info("已调度遗漏交易检查任务，{}分钟后开始，之后每{}分钟执行一次", initialDelayMinutes, CHECK_INTERVAL_MINUTES);
    }

    private static void setTenant() {
        TenantHelper.setDynamic("000000", true);
    }

    /**
     * 从数据库获取所有需要监控的地址
     * 确保数据源一致性，避免Redis地址列表与详情不匹配的问题
     *
     * @return 地址集合
     */
    private Set<String> getAllAddressesFromDatabase() {
        Set<String> addresses = new HashSet<>();
        try {
            setTenant();
            // 直接从数据库获取所有钱包数据
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();

            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                // 添加SOL主地址
                String solAddress = wallet.getCstAddress();
                if (solAddress != null && !solAddress.isEmpty()) {
                    addresses.add(solAddress);
                }

                // 添加USDT地址
                String usdtAddress = wallet.getCstUsdtAddress();
                if (usdtAddress != null && !usdtAddress.isEmpty()) {
                    addresses.add(usdtAddress);
                }

                // 添加USDC地址
                String usdcAddress = wallet.getCstUsdcAddress();
                if (usdcAddress != null && !usdcAddress.isEmpty()) {
                    addresses.add(usdcAddress);
                }
            }

            log.debug("从数据库获取到{}个钱包，包含{}个地址", allWallets.size(), addresses.size());
            return addresses;

        } catch (Exception e) {
            log.error("从数据库获取地址列表失败: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    @Override
    public void scheduleAddressChecks() {
        try {
            setTenant();
            // 直接从数据库获取所有需要监控的地址，确保数据一致性
            Set<String> allAddresses = getAllAddressesFromDatabase();
            if (allAddresses.isEmpty()) {
                log.info("没有地址需要检查遗漏交易");
                return;
            }

            log.info("计划检查{}个地址的遗漏交易", allAddresses.size());

            // 检查是否超过API请求限制
            long currentCount = dailyApiRequestCount.get();
            if (currentCount >= MAX_DAILY_API_REQUESTS) {
                log.warn("已达到每日API请求上限({}次)，暂停遗漏交易检查", MAX_DAILY_API_REQUESTS);
                return;
            }

            // 计算可用的请求数量
            long availableRequests = Math.min(
                MAX_DAILY_API_REQUESTS - currentCount,
                API_REQUESTS_PER_HOUR
            );

            // 计算本次可以检查的地址数量
            int addressesToCheck = (int) Math.min(allAddresses.size(), availableRequests);

            // 将地址转换为列表并随机排序，避免总是从同一地址开始检查
            List<String> addressList = new ArrayList<>(allAddresses);
            Collections.shuffle(addressList);

            // 截取需要检查的地址
            List<String> selectedAddresses = addressList.subList(0, addressesToCheck);

            // 计算检查间隔
            long intervalMs = (CHECK_INTERVAL_MINUTES * 60 * 1000) / (addressesToCheck + 1);

            // 计划检查任务
            log.info("本周期将检查{}个地址，间隔{}毫秒", addressesToCheck, intervalMs);

            AtomicInteger counter = new AtomicInteger(0);
            for (String address : selectedAddresses) {
                int delay = (int) (intervalMs * counter.incrementAndGet());

                transactionCheckScheduler.schedule(() -> {
                    try {
                        setTenant();
                        checkAddressForMissedTransactions(address);
                    } catch (Exception e) {
                        log.error("检查地址{}的遗漏交易时发生错误", address, e);
                    }
                }, delay, TimeUnit.MILLISECONDS);
            }

        } catch (Exception e) {
            log.error("安排地址检查时发生错误", e);
        }
    }

    @Override
    public void checkAddressForMissedTransactions(String address) {
        try {
            log.info("开始处理地址[{}]的遗漏交易补偿", address);
            totalCompensationAttempts.incrementAndGet();

            // 增加API请求计数
            if (dailyApiRequestCount.incrementAndGet() > MAX_DAILY_API_REQUESTS) {
                log.warn("已达到每日API请求上限，跳过地址{}的检查", address);
                failedCompensations.incrementAndGet();
                return;
            }

            // 记录补偿时间和次数
            lastAddressCompensation.put(address, LocalDateTime.now());
            addressCompensationCount.computeIfAbsent(address, k -> new AtomicInteger(0)).incrementAndGet();

            log.debug("检查地址{}的遗漏交易", address);

            // 获取该地址最近的交易记录
            List<org.p2p.solanaj.rpc.types.SignatureInformation> signatures =
                solHttpManager.getSignaturesForAddress(address, MAX_TRANSACTION_HISTORY);

            if (signatures.isEmpty()) {
                log.info("地址[{}]没有可补偿的交易记录", address);
                // 没有交易记录也算成功，因为没有需要补偿的
                successfulCompensations.incrementAndGet();
                return;
            }

            log.info("地址[{}]找到[{}]条历史交易记录，准备补偿处理", address, signatures.size());

            // 获取地址详情，如果不存在则尝试修复
            Map<String, String> detail = solMonitorManager.getOrRepairAddressDetail(address);
            if (detail.isEmpty()) {
                log.warn("地址[{}]的详情信息无法获取或修复，跳过处理", address);
                failedCompensations.incrementAndGet();
                return;
            }

            // 提取必要的信息
            String idStr = detail.get("id");
            if (idStr == null) {
                log.warn("地址[{}]的ID信息不存在，跳过处理", address);
                failedCompensations.incrementAndGet();
                return;
            }

            processTransactionsWithDelay(address, signatures);

            // 记录补偿成功
            successfulCompensations.incrementAndGet();

            // 输出当前补偿统计信息
            logCompensationStats();

        } catch (Exception e) {
            failedCompensations.incrementAndGet();
            log.error("检查地址{}的遗漏交易时发生错误", address, e);

            // 异常时也输出统计信息
            logCompensationStats();
        }
    }

    /**
     * 输出补偿统计信息
     */
    private void logCompensationStats() {
        long attempts = totalCompensationAttempts.get();
        long success = successfulCompensations.get();
        long failed = failedCompensations.get();
        long txCount = totalTransactionsCompensated.get();

        double successRate = attempts > 0 ? (double) success / attempts * 100 : 0;

        log.info("补偿统计 - 总尝试次数: {}, 成功: {}, 失败: {}, 成功率: {}%, 总补偿交易数: {}",
            attempts, success, failed, String.format("%.2f", successRate), txCount);
    }

    /**
     * 处理交易并在处理间添加延迟
     *
     * @param address    地址
     * @param signatures 交易签名列表
     */
    private void processTransactionsWithDelay(String address, List<org.p2p.solanaj.rpc.types.SignatureInformation> signatures) {
        int processedCount = 0;
        int successCount = 0;

        log.info("开始处理地址[{}]的[{}]条交易记录", address, signatures.size());

        // 处理每个交易
        for (org.p2p.solanaj.rpc.types.SignatureInformation signatureInfo : signatures) {
            String signature = signatureInfo.getSignature();

            try {
                // 调用交易处理方法，传入地址信息
                log.debug("处理地址[{}]的交易[{}], 进度: {}/{}", address, signature, processedCount+1, signatures.size());
                solTransactionManager.callTransactionBySignature(signature, address);
                processedCount++;
                successCount++;
                totalTransactionsCompensated.incrementAndGet();

                // 记录详细日志
                log.info("地址[{}]的交易[{}]补偿成功", address, signature);
            } catch (Exception e) {
                log.error("地址[{}]处理交易[{}]时发生错误: {}", address, signature, e.getMessage());
                processedCount++;
            }

            // 避免过快处理
            if (processedCount < signatures.size() && !sleepBetweenTransactions(200)) {
                log.warn("处理地址{}的交易过程被中断，已处理{}条记录", address, processedCount);
                break; // 中断处理循环
            }
        }

        log.info("地址[{}]的遗漏交易检查完成，处理了{}条交易记录，成功{}条",
            address, processedCount, successCount);
    }

    /**
     * 在交易处理间添加延迟
     *
     * @param milliseconds 延迟毫秒数
     * @return 如果成功睡眠返回true，如果被中断返回false
     */
    private boolean sleepBetweenTransactions(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
            return true;
        } catch (InterruptedException e) {
            // 重新设置中断状态
            Thread.currentThread().interrupt();
            return false;
        }
    }

    @Override
    public void scanAllAddressesForMissedTransactions() {
        try {
            log.info("开始全量扫描所有地址的遗漏交易...");

            // 准备扫描环境
            ScanContext scanContext = prepareAddressScan();
            if (scanContext == null) {
                return; // 没有要扫描的地址，提前返回
            }

            performParallelAddressScan(scanContext);

        } catch (Exception e) {
            log.error("执行全量扫描时发生错误", e);
        }
    }

    /**
     * 执行并行地址扫描
     *
     * @param scanContext 扫描上下文
     */
    private void performParallelAddressScan(ScanContext scanContext) {
        // 使用固定线程池并行扫描，控制并行度
        int threadCount = Math.min(5, scanContext.addresses.size());
        ExecutorService scanExecutor = null;

        try {
            // 创建线程池
            scanExecutor = createScanExecutor(threadCount);

            // 提交所有扫描任务
            for (String address : scanContext.addresses) {
                scanExecutor.submit(() -> {
                    setTenant();
                    scanSingleAddress(address, scanContext.processedAddressCount,
                        scanContext.totalTransactionsProcessed, scanContext.totalAddressCount, scanContext.scanLatch);
                });
            }

            // 等待所有扫描任务完成
            waitForScanCompletion(scanContext);

        } finally {
            // 确保线程池被关闭
            if (scanExecutor != null && !scanExecutor.isShutdown()) {
                scanExecutor.shutdown();
            }
        }
    }

    /**
     * 创建用于扫描的线程池
     *
     * @param threadCount 线程数
     * @return 线程池
     */
    private ExecutorService createScanExecutor(int threadCount) {
        return Executors.newFixedThreadPool(threadCount, r -> {
            Thread thread = new Thread(r, "address-scan-worker");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 准备地址扫描的上下文环境
     *
     * @return 扫描上下文，如果没有地址需要扫描则返回null
     */
    private ScanContext prepareAddressScan() {
        // 重置每日API请求计数器，确保有足够的配额进行全量扫描
        dailyApiRequestCount.set(0);

        // 直接从数据库获取所有监控地址，确保数据一致性
        Set<String> addresses = getAllAddressesFromDatabase();
        if (addresses.isEmpty()) {
            log.info("没有需要扫描的地址");
            return null;
        }

        log.info("发现{}个需要扫描的地址", addresses.size());

        // 创建扫描上下文
        ScanContext context = new ScanContext();
        context.addresses = addresses;
        context.totalAddressCount = addresses.size();
        context.processedAddressCount = new AtomicInteger(0);
        context.totalTransactionsProcessed = new AtomicInteger(0);
        context.scanLatch = new CountDownLatch(addresses.size());

        return context;
    }

    /**
     * 扫描单个地址的遗漏交易
     *
     * @param address                    要扫描的地址
     * @param processedAddressCount      已处理地址计数器
     * @param totalTransactionsProcessed 已处理交易计数器
     * @param totalAddressCount          总地址数
     * @param scanLatch                  等待所有地址扫描完成的闭锁
     */
    private void scanSingleAddress(String address, AtomicInteger processedAddressCount,
                                   AtomicInteger totalTransactionsProcessed, int totalAddressCount, CountDownLatch scanLatch) {
        try {
            setTenant();

            // 获取地址详情，如果不存在则尝试修复
            Map<String, String> detail = solMonitorManager.getOrRepairAddressDetail(address);
            if (detail.isEmpty() || detail.get("id") == null) {
                log.warn("地址{}的详情信息无法获取或修复，跳过扫描", address);
                return;
            }

            // 获取该地址的历史交易
            List<org.p2p.solanaj.rpc.types.SignatureInformation> signatures =
                solHttpManager.getSignaturesForAddress(address, MAX_TRANSACTION_HISTORY);

            // 原子递增API请求计数
            dailyApiRequestCount.incrementAndGet();

            if (signatures.isEmpty()) {
                log.debug("地址{}没有交易记录", address);
                return;
            }

            // 处理所有交易
            for (org.p2p.solanaj.rpc.types.SignatureInformation signatureInfo : signatures) {
                String signature = signatureInfo.getSignature();
                solTransactionManager.callTransactionBySignature(signature, address);
                totalTransactionsProcessed.incrementAndGet();
                // 避免请求过快，添加小延迟
                ThreadUtil.sleep(100);
            }

            // 更新进度
            updateScanProgress(processedAddressCount, totalTransactionsProcessed, totalAddressCount);

        } catch (Exception e) {
            log.error("扫描地址{}时发生错误", address, e);
        } finally {
            scanLatch.countDown();
        }
    }

        /**
     * 更新扫描进度
     *
     * @param processedAddressCount      已处理地址计数器
     * @param totalTransactionsProcessed 已处理交易计数器
     * @param totalAddressCount          总地址数
     */
    private void updateScanProgress(AtomicInteger processedAddressCount,
                                    AtomicInteger totalTransactionsProcessed, int totalAddressCount) {
        int completed = processedAddressCount.incrementAndGet();

        if (completed % 10 == 0 || completed == totalAddressCount) {
            log.info("全量扫描进度: {}/{}地址, 处理了{}条交易",
                completed, totalAddressCount, totalTransactionsProcessed.get());
        }
    }

    /**
     * 等待所有扫描任务完成
     *
     * @param context 扫描上下文
     */
    private void waitForScanCompletion(ScanContext context) {
        try {
            boolean completed = context.scanLatch.await(30, TimeUnit.MINUTES);

            if (completed) {
                log.info("全量扫描完成，共扫描{}个地址，处理了{}条交易记录",
                    context.addresses.size(), context.totalTransactionsProcessed.get());
            } else {
                log.warn("全量扫描未在预定时间内完成，已处理{}个地址，{}条交易记录",
                    context.processedAddressCount.get(), context.totalTransactionsProcessed.get());
            }
        } catch (InterruptedException e) {
            // 重新设置中断状态
            Thread.currentThread().interrupt();
            log.warn("全量扫描等待过程被中断，已处理{}个地址，{}条交易记录",
                context.processedAddressCount.get(), context.totalTransactionsProcessed.get());
        }
    }

    @Override
    public void checkLongDisconnection(long reconnectTime, long lastDisconnectTime) {
        if (lastDisconnectTime > 0 && reconnectTime - lastDisconnectTime > 60000) {
            // 如果距离上次断连超过60秒，则是较长时间的断连，触发全量扫描
            log.info("检测到长时间断连后的重连，等待地址订阅完成后将进行全量扫描");

            // 等待订阅完成后触发扫描
            transactionCheckScheduler.schedule(() -> {
                log.info("长时间断连恢复后开始全量扫描...");
                scanAllAddressesForMissedTransactions();
            }, 15, TimeUnit.SECONDS);
        }
    }

    @Override
    public void triggerCompensationOnConnection() {
        // 使用并发控制，避免与定时任务冲突
        if (compensationRunning.compareAndSet(false, true)) {
            try {
                setTenant();
                log.info("WebSocket连接成功触发：立即执行遗漏交易补偿检查");

                // 延迟5秒执行，确保地址订阅已完成
                transactionCheckScheduler.schedule(() -> {
                    try {
                        setTenant();
                        scheduleAddressChecks();
                        log.info("连接触发的补偿检查执行完成");
                    } catch (Exception e) {
                        log.error("连接触发的补偿检查执行异常", e);
                    } finally {
                        compensationRunning.set(false);
                    }
                }, 5, TimeUnit.SECONDS);

            } catch (Exception e) {
                compensationRunning.set(false);
                log.error("触发连接补偿检查失败", e);
            }
        } else {
            log.info("补偿任务正在执行中，跳过连接触发的补偿检查");
        }
    }

    @Override
    public void resetScheduler() {
        log.info("重置定时任务调度器，重新开始60分钟周期");
        scheduleNextCompensationCheck();
    }

    @Override
    public void shutdown() {
        // 取消定时任务
        if (scheduledTask != null && !scheduledTask.isDone()) {
            scheduledTask.cancel(false);
        }

        if (transactionCheckScheduler != null && !transactionCheckScheduler.isShutdown()) {
            transactionCheckScheduler.shutdown();
            log.info("交易扫描调度器已关闭");
        }
    }

    /**
     * 地址扫描上下文，用于在各个方法间传递数据
     */
    private static class ScanContext {
        Set<String> addresses;
        int totalAddressCount;
        AtomicInteger processedAddressCount;
        AtomicInteger totalTransactionsProcessed;
        CountDownLatch scanLatch;
    }

    /**
     * 获取补偿统计信息
     *
     * @return 补偿统计信息Map
     */
    @Override
    public Map<String, Object> getCompensationStats() {
        Map<String, Object> stats = new HashMap<>();

        // 计算成功率
        long attempts = totalCompensationAttempts.get();
        long success = successfulCompensations.get();
        double successRate = attempts > 0 ? (double) success / attempts * 100 : 0;

        // 基本统计信息
        stats.put("totalAttempts", attempts);
        stats.put("successfulCompensations", success);
        stats.put("failedCompensations", failedCompensations.get());
        stats.put("successRate", String.format("%.2f%%", successRate));
        stats.put("totalTransactionsCompensated", totalTransactionsCompensated.get());
        stats.put("dailyApiRequestCount", dailyApiRequestCount.get());
        stats.put("apiCounterResetTime", apiCounterResetTime.toString());

        // 最近补偿的地址信息（限制为最近10个）
        Map<String, Object> recentAddresses = new HashMap<>();
        lastAddressCompensation.entrySet().stream()
            .sorted(Map.Entry.<String, LocalDateTime>comparingByValue().reversed())
            .limit(10)
            .forEach(entry -> {
                Map<String, Object> addressInfo = new HashMap<>();
                addressInfo.put("lastCompensationTime", entry.getValue().toString());
                addressInfo.put("compensationCount", addressCompensationCount.getOrDefault(entry.getKey(), new AtomicInteger(0)).get());
                recentAddresses.put(entry.getKey(), addressInfo);
            });

        stats.put("recentAddressCompensations", recentAddresses);

        return stats;
    }

    /**
     * 重置补偿统计信息
     */
    @Override
    public void resetCompensationStats() {
        totalCompensationAttempts.set(0);
        successfulCompensations.set(0);
        failedCompensations.set(0);
        totalTransactionsCompensated.set(0);
        lastAddressCompensation.clear();
        addressCompensationCount.clear();
        log.info("补偿统计信息已重置");
    }
}
