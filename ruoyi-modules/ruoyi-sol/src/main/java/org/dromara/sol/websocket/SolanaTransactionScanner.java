package org.dromara.sol.websocket;

import java.util.Map;

/**
 * Solana交易扫描器
 * 负责扫描历史交易，实现补偿机制
 */
public interface SolanaTransactionScanner {

    /**
     * 启动遗漏交易检查任务
     * 将在一个小时内均匀分布检查所有地址
     */
    void startMissedTransactionChecker();

    /**
     * 扫描所有监控地址的遗漏交易
     * 用于服务启动或长时间断连后重连时的全量检查
     */
    void scanAllAddressesForMissedTransactions();

    /**
     * 均匀分配地址检查任务
     * 将所有需要检查的地址分散到整个检查周期中
     */
    void scheduleAddressChecks();

    /**
     * 检查指定地址的遗漏交易
     *
     * @param address 要检查的地址
     */
    void checkAddressForMissedTransactions(String address);

    /**
     * 检查是否是长时间断连后的重连
     * 如果是，则触发全量扫描
     *
     * @param reconnectTime 重连时间
     * @param lastDisconnectTime 上次断连时间
     */
    void checkLongDisconnection(long reconnectTime, long lastDisconnectTime);

    /**
     * 关闭扫描器
     * 清理相关资源
     */
    void shutdown();

    /**
     * 在WebSocket连接成功时触发交易补偿检查
     * 立即执行一次遗漏交易检查，无需等待定时任务周期
     */
    void triggerCompensationOnConnection();

    /**
     * 重置定时任务调度器
     * 重新开始60分钟的定时周期
     */
    void resetScheduler();

    /**
     * 获取补偿统计信息
     *
     * @return 补偿统计信息Map
     */
    Map<String, Object> getCompensationStats();

    /**
     * 重置补偿统计信息
     */
    void resetCompensationStats();

    /**
     * 获取全量扫描状态信息
     * 用于监控和调试
     *
     * @return 全量扫描状态信息
     */
    String getFullScanStatus();
}
